import{d as e,aj as t,ai as a,B as l,b as o,r,z as i,y as n,o as u,e as s,w as p,g as c,f as b,c as m,ag as d,u as g,F as k,i as T,aq as y,t as h,Y as v,h as C,q as f,v as S,bt as N,R as w,ay as R,a0 as _,av as P,l as z,aT as D,ax as j}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                       *//* empty css                        */import{b as x}from"./book.api-ERXvEXQF.js";import{o as B}from"./order.api-B-JCVvq6.js";const V={style:{"min-height":"820px",overflow:"auto"}},E={key:0},O={key:1},U={key:2},q={key:3},F={key:4},L=e({__name:"bookDetails",props:{modelValue:{type:Boolean,default:!1},planCheckinTime:{type:String,default:""},planCheckoutTime:{type:String,default:""},deatilRtcode:{type:String,default:""},deatilDate:{type:String,default:""},roomType:{type:String,default:""}},emits:["update:modelValue"],setup(e,{emit:L}){const A=e,G=L,H=_(),{t:M}=t(),Y=a(),I=l({get:()=>A.modelValue,set:e=>G("update:modelValue",e)}),J=o("staying"),K=r({gcode:Y.gcode,hcode:Y.hcode,pageNo:1,pageSize:10,startTime:A.planCheckinTime,endTime:A.planCheckoutTime,rtCode:A.deatilRtcode,date:A.deatilDate}),Q=o([]);i((()=>{let e=[{num:0,label:M("staying"),value:"staying"},{num:0,label:M("booked"),value:"booked"},{num:0,label:M("underRepair"),value:"underRepair"}];"roomTypeForecasting"!=A.roomType&&(e=[...e,{num:0,label:M("preCheckout"),value:"preCheckout"},{num:0,label:M("blockRooms"),value:"blockRooms"}]),Q.value=e}));const W=r({currentPage:1,pageSize:10,total:0,layout:"prev, pager, next"}),X=o(!1),Z=o([]),$=o([]),ee=o([]),te=o([]),ae=o([]);function le(e){return e?P(e).format("MM/DD HH:mm"):"--"}async function oe(){X.value=!0;const{data:e}=await B.orderPagerList({...K,timeType:"2"});X.value=!1,Q.value[0].num=e.total,Z.value=e.list,W.total=e.total,W.currentPage=e.pageNo,W.pageSize=e.pageSize}async function re(){X.value=!0;const{data:e}=await x.getPageBook(K);X.value=!1,Q.value[1].num=e.total,$.value=e.list,W.total=e.total,W.currentPage=e.pageNo,W.pageSize=e.pageSize}async function ie(){X.value=!0;const{data:e}=await B.getRepairRoom(K);X.value=!1,Q.value[2].num=e.total,ee.value=e.list,W.total=e.total,W.currentPage=e.pageNo,W.pageSize=e.pageSize}async function ne(){X.value=!0;const{data:e}=await B.getCheckoutList(K);X.value=!1,Q.value[3].num=e.total,te.value=e.list,W.total=e.total,W.currentPage=e.pageNo,W.pageSize=e.pageSize}async function ue(){X.value=!0;const{data:e}=await B.getBookConflict(K);X.value=!1,Q.value[4].num=e.total,ae.value=e.list,W.total=e.total,W.currentPage=e.pageNo,W.pageSize=e.pageSize}async function se(e,t){if("view"===e){const e={fullscreen:"true",modelValue:"true",tabName:"detail",no:"",noType:""};"staying"!=J.value&&"preCheckout"!=J.value||(e.no=t.orderNo,e.noType="order"),"booked"==J.value&&(e.no=t.bookNo,e.noType="book"),"blockRooms"==J.value&&t.no&&(e.no=t.no,e.noType=t.noType),window.open(H.resolve({name:"orderDetails",query:{...e}}).href,"_blank")}}function pe(e){switch(K.pageNo=e,W.currentPage=e,J.value){case"staying":oe();break;case"booked":re();break;case"preCheckout":ne();break;case"underRepair":ie();break;case"blockRooms":ue()}}function ce(){I.value=!1}function be(e){switch(K.pageNo=1,W.currentPage=1,e){case"staying":oe();break;case"booked":re();break;case"preCheckout":ne();break;case"underRepair":ie();break;case"blockRooms":ue()}}return n((async()=>{"roomTypeForecasting"==A.roomType?await Promise.all([oe(),re(),ie()]):await Promise.all([oe(),re(),ne(),ie(),ue()])})),(e,t)=>{const a=j,l=z,o=D;return u(),s(g(R),{modelValue:g(I),"onUpdate:modelValue":t[6]||(t[6]=e=>T(I)?I.value=e:null),title:g(M)("roomTypeOccupyDetails"),width:"1300px",onClose:ce},{default:p((()=>[c("div",V,[b(l,{modelValue:g(J),"onUpdate:modelValue":t[0]||(t[0]=e=>T(J)?J.value=e:null),class:"mb-[16px]",onChange:be},{default:p((()=>[(u(!0),m(k,null,d(g(Q),((e,t)=>(u(),s(a,{key:t,value:e.value},{default:p((()=>[C(v(e.label)+"("+v(e.num)+") ",1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"]),"staying"===g(J)?y((u(),m("div",E,[b(g(S),{data:g(Z),style:{width:"100%"}},{default:p((()=>[b(g(h),{prop:"rtName",label:g(M)("roomTypeName")},null,8,["label"]),b(g(h),{prop:"rNo",label:g(M)("roomNumber")},null,8,["label"]),b(g(h),{label:g(M)("guestPhone")},{default:p((({row:e})=>[c("div",null,v(e.name),1),c("div",null,v(e.phone),1)])),_:1},8,["label"]),b(g(h),{prop:"checkinTime",label:g(M)("checkinTime")},{default:p((({row:e})=>[C(v(le(e.checkinTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"planCheckoutTime",label:g(M)("planCheckoutTime")},{default:p((({row:e})=>[C(v(le(e.planCheckoutTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"guestSrcTypeName",label:g(M)("guestSource")},null,8,["label"]),b(g(h),{prop:"orderSourceName",label:g(M)("orderSource")},null,8,["label"]),b(g(h),{label:g(M)("actions")},{default:p((({row:e})=>[b(g(f),{link:"",type:"primary",onClick:t=>se("view",e)},{default:p((()=>t[7]||(t[7]=[C(" 查看 ")]))),_:2},1032,["onClick"])])),_:1},8,["label"])])),_:1},8,["data"]),g(W).total>0?(u(),s(g(N),{key:0,"current-page":g(W).currentPage,"onUpdate:currentPage":t[1]||(t[1]=e=>g(W).currentPage=e),"page-size":g(W).pageSize,total:g(W).total,layout:"prev, pager, next",class:"mt-4 justify-center",onCurrentChange:pe},null,8,["current-page","page-size","total"])):w("",!0)])),[[o,g(X)]]):w("",!0),"booked"===g(J)?y((u(),m("div",O,[b(g(S),{data:g($),style:{width:"100%"}},{default:p((()=>[b(g(h),{prop:"rtName",label:g(M)("roomTypeName")},null,8,["label"]),b(g(h),{prop:"rNo",label:g(M)("roomNumber")},null,8,["label"]),b(g(h),{label:g(M)("guestPhone")},{default:p((({row:e})=>[c("div",null,v(e.name),1),c("div",null,v(e.phone),1)])),_:1},8,["label"]),b(g(h),{prop:"planCheckinTime",label:g(M)("planCheckinTime")},{default:p((({row:e})=>[C(v(le(e.planCheckinTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"planCheckoutTime",label:g(M)("planCheckoutTime")},{default:p((({row:e})=>[C(v(le(e.planCheckoutTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"guestSrcTypeName",label:g(M)("guestSource")},null,8,["label"]),b(g(h),{prop:"orderSourceName",label:g(M)("orderSource")},null,8,["label"]),b(g(h),{label:g(M)("actions")},{default:p((({row:e})=>[b(g(f),{link:"",type:"primary",onClick:t=>se("view",e)},{default:p((()=>t[8]||(t[8]=[C(" 查看 ")]))),_:2},1032,["onClick"])])),_:1},8,["label"])])),_:1},8,["data"]),g(W).total>0?(u(),s(g(N),{key:0,"current-page":g(W).currentPage,"onUpdate:currentPage":t[2]||(t[2]=e=>g(W).currentPage=e),"page-size":g(W).pageSize,total:g(W).total,layout:"prev, pager, next",class:"mt-4 justify-center",onCurrentChange:pe},null,8,["current-page","page-size","total"])):w("",!0)])),[[o,g(X)]]):w("",!0),"underRepair"===g(J)?y((u(),m("div",U,[b(g(S),{data:g(ee),style:{width:"100%"}},{default:p((()=>[b(g(h),{prop:"rtName",label:g(M)("roomTypeName")},null,8,["label"]),b(g(h),{prop:"rNo",label:g(M)("roomNumber")},null,8,["label"]),b(g(h),{label:g(M)("guestPhone")},{default:p((({row:e})=>[c("div",null,v(e.name),1),c("div",null,v(e.phone),1)])),_:1},8,["label"]),b(g(h),{prop:"repairStartTime",label:g(M)("repairStartTime")},{default:p((({row:e})=>[C(v(le(e.repairStartTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"repairEndTime",label:g(M)("repairEndTime")},{default:p((({row:e})=>[C(v(le(e.repairEndTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"guestSrcTypeName",label:g(M)("guestSource")},null,8,["label"]),b(g(h),{prop:"orderSourceName",label:g(M)("orderSource")},null,8,["label"])])),_:1},8,["data"]),g(W).total>0?(u(),s(g(N),{key:0,"current-page":g(W).currentPage,"onUpdate:currentPage":t[3]||(t[3]=e=>g(W).currentPage=e),"page-size":g(W).pageSize,total:g(W).total,layout:"prev, pager, next",class:"mt-4 justify-center",onCurrentChange:pe},null,8,["current-page","page-size","total"])):w("",!0)])),[[o,g(X)]]):w("",!0),"preCheckout"===g(J)?y((u(),m("div",q,[b(g(S),{data:g(te),style:{width:"100%"}},{default:p((()=>[b(g(h),{prop:"rtName",label:g(M)("roomTypeName")},null,8,["label"]),b(g(h),{prop:"rNo",label:g(M)("roomNumber")},null,8,["label"]),b(g(h),{label:g(M)("guestPhone")},{default:p((({row:e})=>[c("div",null,v(e.name),1),c("div",null,v(e.phone),1)])),_:1},8,["label"]),b(g(h),{prop:"planCheckinTime",label:g(M)("planCheckinTime")},{default:p((({row:e})=>[C(v(le(e.planCheckinTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"planCheckoutTime",label:g(M)("planCheckoutTime")},{default:p((({row:e})=>[C(v(le(e.planCheckoutTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"guestSrcTypeName",label:g(M)("guestSource")},null,8,["label"]),b(g(h),{prop:"orderSourceName",label:g(M)("orderSource")},null,8,["label"]),b(g(h),{label:g(M)("actions")},{default:p((({row:e})=>[b(g(f),{link:"",type:"primary",onClick:t=>se("view",e)},{default:p((()=>t[9]||(t[9]=[C(" 查看 ")]))),_:2},1032,["onClick"])])),_:1},8,["label"])])),_:1},8,["data"]),g(W).total>0?(u(),s(g(N),{key:0,"current-page":g(W).currentPage,"onUpdate:currentPage":t[4]||(t[4]=e=>g(W).currentPage=e),"page-size":g(W).pageSize,total:g(W).total,layout:"prev, pager, next",class:"mt-4 justify-center",onCurrentChange:pe},null,8,["current-page","page-size","total"])):w("",!0)])),[[o,g(X)]]):w("",!0),"blockRooms"===g(J)?y((u(),m("div",F,[b(g(S),{data:g(ae),style:{width:"100%"}},{default:p((()=>[b(g(h),{prop:"rtName",label:g(M)("roomTypeName")},null,8,["label"]),b(g(h),{prop:"rNo",label:g(M)("roomNumber")},null,8,["label"]),b(g(h),{prop:"state",label:g(M)("state")},null,8,["label"]),b(g(h),{label:g(M)("guestPhone")},{default:p((({row:e})=>[c("div",null,v(e.name?e.name:"--"),1),c("div",null,v(e.phone),1)])),_:1},8,["label"]),b(g(h),{prop:"checkinTypeName",label:g(M)("reservationType")},null,8,["label"]),b(g(h),{prop:"num",label:g(M)("roomsBooked")},null,8,["label"]),b(g(h),{prop:"startTime",label:g(M)("checkinTime1")},{default:p((({row:e})=>[C(v(le(e.startTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"endTime",label:g(M)("checkoutTime")},{default:p((({row:e})=>[C(v(le(e.endTime)),1)])),_:1},8,["label"]),b(g(h),{prop:"no",label:g(M)("orderNumber"),"min-width":"170"},null,8,["label"]),b(g(h),{label:g(M)("actions")},{default:p((({row:e})=>[e.no?(u(),s(g(f),{key:0,link:"",type:"primary",onClick:t=>se("view",e)},{default:p((()=>t[10]||(t[10]=[C(" 查看 ")]))),_:2},1032,["onClick"])):w("",!0)])),_:1},8,["label"])])),_:1},8,["data"]),g(W).total>0?(u(),s(g(N),{key:0,"current-page":g(W).currentPage,"onUpdate:currentPage":t[5]||(t[5]=e=>g(W).currentPage=e),"page-size":g(W).pageSize,total:g(W).total,layout:"prev, pager, next",class:"mt-4 justify-center",onCurrentChange:pe},null,8,["current-page","page-size","total"])):w("",!0)])),[[o,g(X)]]):w("",!0),c("div",null,[c("div",null,"1、"+v(g(M)("availableRoomsDesc")),1),c("div",null,"2、"+v(g(M)("lockedRoomsDesc")),1)])])])),_:1},8,["modelValue","title"])}}});function A(e){const t=e;t.__i18n=t.__i18n||[],t.__i18n.push({locale:"",resource:{en:{roomTypeOccupyDetails:{t:0,b:{t:2,i:[{t:3}],s:"Room Type Occupancy Details"}},roomTypeName:{t:0,b:{t:2,i:[{t:3}],s:"Room Type Name"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Room Number"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"Guest / Phone"}},planCheckinTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Check-in Time"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Planned Check-out Time"}},repairStartTime:{t:0,b:{t:2,i:[{t:3}],s:"Repair Start Time"}},repairEndTime:{t:0,b:{t:2,i:[{t:3}],s:"Repair End Time"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"Checkin Time"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"Guest Source"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"Order Source"}},repairReason:{t:0,b:{t:2,i:[{t:3}],s:"Repair Reason"}},availableRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"Available rooms = Total rooms - Occupied - Booked + Checked-out - Under repair"}},lockedRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"Locked rooms do not occupy inventory"}},startTime:{t:0,b:{t:2,i:[{t:3}],s:"Start Time"}},endTime:{t:0,b:{t:2,i:[{t:3}],s:"End Time"}},staying:{t:0,b:{t:2,i:[{t:3}],s:"Staying"}},booked:{t:0,b:{t:2,i:[{t:3}],s:"Booked"}},preCheckout:{t:0,b:{t:2,i:[{t:3}],s:"preCheckout"}},underRepair:{t:0,b:{t:2,i:[{t:3}],s:"Repair"}},blockRooms:{t:0,b:{t:2,i:[{t:3}],s:"Block rooms"}},state:{t:0,b:{t:2,i:[{t:3}],s:"state"}},reservationType:{t:0,b:{t:2,i:[{t:3}],s:"Reservation Type"}},roomsBooked:{t:0,b:{t:2,i:[{t:3}],s:"Rooms Booked"}},checkinTime1:{t:0,b:{t:2,i:[{t:3}],s:"Check-in Time"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"Check-out Time"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"Order Number"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"Actions"}},view:{t:0,b:{t:2,i:[{t:3}],s:"View"}}},"zh-cn":{roomTypeOccupyDetails:{t:0,b:{t:2,i:[{t:3}],s:"房型占用详情"}},roomTypeName:{t:0,b:{t:2,i:[{t:3}],s:"房型名称"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"客人/电话"}},planCheckinTime:{t:0,b:{t:2,i:[{t:3}],s:"预抵时间"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"预离时间"}},repairStartTime:{t:0,b:{t:2,i:[{t:3}],s:"维修开始时间"}},repairEndTime:{t:0,b:{t:2,i:[{t:3}],s:"维修结束时间"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"入住时间"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"客源"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"订单来源"}},repairReason:{t:0,b:{t:2,i:[{t:3}],s:"维修原因"}},availableRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"可售数 = 总房数 - 在住 - 预订 + 预离 - 维修"}},lockedRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"锁房不占用库存"}},startTime:{t:0,b:{t:2,i:[{t:3}],s:"开始时间"}},endTime:{t:0,b:{t:2,i:[{t:3}],s:"结束时间"}},staying:{t:0,b:{t:2,i:[{t:3}],s:"在住"}},booked:{t:0,b:{t:2,i:[{t:3}],s:"预订"}},preCheckout:{t:0,b:{t:2,i:[{t:3}],s:"预离"}},underRepair:{t:0,b:{t:2,i:[{t:3}],s:"维修"}},blockRooms:{t:0,b:{t:2,i:[{t:3}],s:"占房"}},state:{t:0,b:{t:2,i:[{t:3}],s:"状态"}},reservationType:{t:0,b:{t:2,i:[{t:3}],s:"入住类型"}},roomsBooked:{t:0,b:{t:2,i:[{t:3}],s:"占用间数"}},checkinTime1:{t:0,b:{t:2,i:[{t:3}],s:"起始时间"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"结束时间"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"订单号"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},view:{t:0,b:{t:2,i:[{t:3}],s:"查看"}}},km:{roomTypeOccupyDetails:{t:0,b:{t:2,i:[{t:3}],s:"ព័ត៌មានលម្អិតអំពីការប្រើប្រាស់ប្រភេទបន្ទប់"}},roomTypeName:{t:0,b:{t:2,i:[{t:3}],s:"ឈ្មោះប្រភេទបន្ទប់"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},guestPhone:{t:0,b:{t:2,i:[{t:3}],s:"ភ្ញៀវ/ទូរស័ព្ទ"}},planCheckinTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាមកដល់ដែលបានគ្រោងទុក"}},planCheckoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចេញដែលបានគ្រោងទុក"}},repairStartTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចាប់ផ្តើមជួសជុល"}},repairEndTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាបញ្ចប់ជួសជុល"}},checkinTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចូលស្នាក់នៅ"}},guestSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពភ្ញៀវ"}},orderSource:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភពលំដាប់"}},repairReason:{t:0,b:{t:2,i:[{t:3}],s:"មូលហេតុជួសជុល"}},availableRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលអាចលក់បាន = ចំនួនបន្ទប់សរុប - កំពុងស្នាក់នៅ - បានកក់ + កំពុងចេញ - កំពុងជួសជុល"}},lockedRoomsDesc:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដែលបានចាក់សោមិនប្រើប្រាស់ស្តុក"}},startTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចាប់ផ្តើម"}},endTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាបញ្ចប់"}},staying:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងស្នាក់នៅ"}},booked:{t:0,b:{t:2,i:[{t:3}],s:"បានកក់"}},preCheckout:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងចេញ"}},underRepair:{t:0,b:{t:2,i:[{t:3}],s:"កំពុងជួសជុល"}},blockRooms:{t:0,b:{t:2,i:[{t:3}],s:"កាន់កាប់បន្ទប់"}},state:{t:0,b:{t:2,i:[{t:3}],s:"ស្ថានភាព"}},reservationType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទការស្នាក់នៅ"}},roomsBooked:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនបន្ទប់ដែលកាន់កាប់"}},checkinTime1:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាចាប់ផ្តើម"}},checkoutTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាបញ្ចប់"}},orderNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខលំដាប់"}},actions:{t:0,b:{t:2,i:[{t:3}],s:"សកម្មភាព"}},view:{t:0,b:{t:2,i:[{t:3}],s:"មើល"}}}}})}A(L);export{L as default};
//# sourceMappingURL=bookDetails-LczAhzor.js.map
