<i18n>
{
  "en": {
    "roomType": "Type",
    "roomState": "Status",
    "other": "Other",
    "preOrderedRoom": "Pre-ordered Rooms",
    "roomAllocated": "Room Allocated",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "getRoomTypesFailed": "Failed to get room type list",
    "maxSelectRooms": "Max select up to {roomNum} rooms",
    "selectAtLeastOneRoom": "At least one room must be selected"
  },
  "zh-cn": {
    "roomType": "房型",
    "roomState": "房态",
    "other": "其他",
    "preOrderedRoom": "预订单占用房间",
    "roomAllocated": "已排房",
    "cancel": "取消",
    "confirm": "确定",
    "getRoomTypesFailed": "获取房型列表失败",
    "maxSelectRooms": "最多选择{roomNum}间房间",
    "selectAtLeastOneRoom": "至少选中一间房",
    "rooms": "房间"
  },
  "km": {
    "roomType": "ប្រភេទបន្ទប់",
    "roomState": "ស្ថានភាពបន្ទប់",
    "other": "ផ្សេងៗ",
    "preOrderedRoom": "បន្ទប់ដែលបានកក់ទុកជាមុន",
    "roomAllocated": "បន្ទប់ដែលបានចែកចាយ",
    "cancel": "បោះបង់",
    "confirm": "បញ្ជាក់",
    "getRoomTypesFailed": "បរាជ័យក្នុងការទទួលបានបញ្ជីប្រភេទបន្ទប់",
    "maxSelectRooms": "អាចជ្រើសរើសបានអតិបរមា {roomNum} បន្ទប់",
    "selectAtLeastOneRoom": "ត្រូវជ្រើសរើសយ៉ាងហោចណាស់មួយបន្ទប់",
    "rooms": "បន្ទប់"
  }
}
</i18n>

<script setup lang="ts">
import { bookApi, dictDataApi, rtApi } from '@/api/modules/index'
import { CheckinType, DICT_TYPE_ROOM_STATUS, RoomState } from '@/models/index'
import useUserStore from '@/store/modules/user'
import dayjs from 'dayjs'

const props = withDefaults(
  defineProps<{
    modelValue?: boolean
    /** 预订单号 */
    bookNo: string
    /** 批次号 */
    batchNo: string
    /** 订单号 */
    orderNo: string
    /** 房间类型 */
    rtCode: string
    /** 房间类型名称 */
    rtName: string
    /** 房间号列表 */
    rNos: string[]
    /** 预抵时间 */
    planCheckinTime: string
    /** 预离时间 */
    planCheckoutTime: string
    /** 预订占用房间 */
    isBookedRoom: string
    checkinType: string
    hourCode: string
    guestSrcTypeName: string
    guestSrcType: string
    guestCode: string
    channelCode: string
    orderSource: string
  }>(),
  {
    modelValue: false,
    bookNo: '',
    batchNo: '',
    orderNo: '',
    rtCode: '',
    rtName: '',
    rNos: () => [],
    planCheckinTime: '',
    planCheckoutTime: '',
    isBookedRoom: '',
    checkinType: '',
    hourCode: '',
    guestSrcTypeName: '',
    guestSrcType: '',
    guestCode: '',
    channelCode: '',
    orderSource: '',
  }
)
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
  selected: [data: any]
}>()
const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(false)
const data = ref({
  rtCode: props.rtCode,
  rtName: props.rtName,
  /** 房间状态 VC 空净  VD 空脏  OC 住净 OD 住脏  OO维修 */
  state: 'VC',
  /** 预订单占用房间 0:否 1:是 */
  isBookedRoom: '0',
  selectRooms: props.rNos,
  dayPrices: [] as { date: string; price: string }[],
  planCheckinTime: props.planCheckinTime,
  planCheckoutTime: props.planCheckoutTime,
  roomNum: 1,
})
const roomNum = ref<number>(1)
/** 房间列表 */
const roomPrices = ref<{ rNo: string; rCode: string }[]>([])
const roomAll = ref<{ rNo: string; rCode: string; state: string }[]>([])

onMounted(async () => {
  getConstants()
  if (props.orderNo === '') {
    getRts()
  } else {
    getRooms()
  }
})
/** 房型列表 */
const rts = ref<{ rtCode: string; rtName: string; price: string | number }[]>([])
async function getRts() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    delayMinute: 0,
    channelCode: props.channelCode,
    orderSource: props.orderSource,
    planCheckinTime: props.planCheckinTime,
    planCheckoutTime: props.planCheckoutTime,
    checkinType: props.checkinType,
    hourCode: props.checkinType === CheckinType.HOUR_ROOM ? props.hourCode : null,
    guestSrcTypeName: props.guestSrcTypeName,
    guestSrcType: props.guestSrcType,
    guestCode: props.guestCode,
  }
  await bookApi.roomtypeList(params).then((res: any) => {
    if (res.code !== 0) {
      ElMessage.error(t('getRoomTypesFailed'))
      return
    }
    rts.value = res.data
    if (rts.value.length > 0 && props.orderNo === '' && props.rtCode === '') {
      data.value.rtCode = rts.value[0].rtCode
      data.value.rtName = rts.value[0].rtName
    }
    if (props.isBookedRoom === '1') {
      data.value.state = ''
      data.value.isBookedRoom = props.isBookedRoom
    }
    getRooms()
  })
}
// 通用字典
const dictTypes = [DICT_TYPE_ROOM_STATUS]
/** 房间状态 */
const roomStates = ref<{ code: string; label: string }[]>([])

function getConstants() {
  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {
    roomStates.value = res.data.filter((item: any) => item.code !== 'OO')
  })
}
function getRoomChange() {
  if (data.value.state) {
    data.value.isBookedRoom = '0'
  }
  roomPrices.value = roomAll.value.filter((item: { state: string }) => {
    return item.state === data.value.state
  })
}
async function getRooms() {
  loading.value = true
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    rtCode: data.value.rtCode,
    state: data.value.state,
    planCheckinTime: dayjs(props.planCheckinTime).format('YYYY-MM-DD HH:mm'),
    planCheckoutTime: dayjs(props.planCheckoutTime).format('YYYY-MM-DD HH:mm'),
    isMeetingRoom: '0',
    preOccupied: data.value.isBookedRoom,
  }
  await bookApi.canBookRoomList(params).then((res: any) => {
    loading.value = false
    if (res.code !== 0) {
      ElMessage.error(t('getRoomTypesFailed'))
      return
    }
    roomAll.value = res.data
    roomPrices.value = res.data.filter((item: { state: string }) => {
      return item.state === data.value.state
    })
  })
}

function handleClose(tag: string) {
  data.value.selectRooms.splice(data.value.selectRooms.indexOf(tag), 1)
}

function doCheck() {
  if (roomNum.value > 0 && data.value.selectRooms.length > roomNum.value) {
    ElMessage.warning(t('maxSelectRooms', { roomNum: roomNum.value }))
    data.value.selectRooms = data.value.selectRooms.slice(0, roomNum.value)
    return false
  } else {
    return true
  }
}

function bookedRoomChange() {
  if (data.value.isBookedRoom === '1') {
    data.value.state = '' as RoomState
  } else {
    data.value.state = RoomState.VC
  }
  getRooms()
}

function selectRoomType(rtCode: string) {
  // 如果点击的是已选中的房型，不执行任何操作
  if (data.value.rtCode === rtCode) {
    return
  }
  data.value.rtCode = rtCode
  getRooms()
}
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})
function onSubmit() {
  if (props.orderNo) {
    let list = []
    list = roomPrices.value.filter((person) => data.value.selectRooms.includes(person.rNo))
    const params = {
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      bookNo: props.bookNo,
      batchNo: props.batchNo,
      rooms: [
        {
          rCode: list.length ? list[0].rCode : '',
          rNo: list.length ? list[0].rNo : '',
          orderNo: props.orderNo,
        },
      ],
    }
    bookApi.arrangeBook(params).then((res: any) => {
      if (res.code === 0) {
        emits('success')
        onCancel()
      } else {
        ElMessage.error({
          message: res.msg,
          center: true,
        })
      }
    })
  } else {
    let list = []
    let roomList = []
    list = roomPrices.value.filter((person) => data.value.selectRooms.includes(person.rNo))
    roomList = rts.value.filter((item: any) => item.rtCode === data.value.rtCode)
    if (list.length > 0) {
      const params = {
        rtName: roomList[0].rtName,
        rtCode: roomList[0].rtCode,
        rCode: list.length ? list[0].rCode : '',
        rNo: list.length ? list[0].rNo : '',
        isBookedRoom: data.value.isBookedRoom === '1' ? data.value.isBookedRoom : '',
      }
      emits('selected', params)
      onCancel()
    } else {
      ElMessage.error({
        message: t('selectAtLeastOneRoom'),
        center: true,
      })
    }
  }
}
function onCancel() {
  myVisible.value = false
}
</script>

<template>
  <div>
    <el-dialog v-model="myVisible" :title="t('roomAllocated')" width="600px" :close-on-click-modal="false" append-to-body destroy-on-close @keydown.ctrl.enter.prevent="onSubmit">
      <el-form size="default" label-width="50px" inline-message inline class="search-form">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomType')" label-width="80px">
              <div class="room-type-buttons">
                <el-button v-if="props.orderNo" type="primary" size="small">
                  {{ props.rtName }}
                </el-button>
                <el-button v-for="item in rts" v-else :key="item.rtCode" size="small" :type="data.rtCode === item.rtCode ? 'primary' : 'default'" @click="selectRoomType(item.rtCode)">
                  {{ item.rtName }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomState')" label-width="80px">
              <el-radio-group v-model="data.state" size="small" @change="getRoomChange">
                <el-radio-button v-for="item in roomStates" :key="item.code" :value="item.code" border>
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('other')" label-width="80px">
              <el-checkbox v-model="data.isBookedRoom" true-value="1" false-value="0" size="small" :label="t('preOrderedRoom')" border @change="bookedRoomChange()" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="special_td">
        <div style="margin-bottom: 15px">
          <span v-if="roomNum > 0">{{ t('roomAllocated') }}: {{ data.selectRooms.length }} / {{ roomNum }} {{ t('rooms') }}</span>
          <span v-else>{{ t('roomAllocated') }}: {{ data.selectRooms.length }}{{ t('rooms') }}</span>
        </div>
        <el-tag v-for="item in data.selectRooms" :key="item" class="roomtag" type="danger" closable @close="handleClose(item)">
          {{ item }}
        </el-tag>
      </div>
      <div class="roomList">
        <div class="flexBox">
          <ul style="padding-left: 0; margin-bottom: 0; list-style: none">
            <el-checkbox-group v-model="data.selectRooms">
              <li v-for="item in roomPrices" :key="item.rCode" class="xxx">
                {{ item }}
                <el-checkbox v-model="item.rNo" :value="item.rNo" border @change="doCheck">
                  {{ item.rNo }}
                </el-checkbox>
              </li>
            </el-checkbox-group>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button size="large" @click="onCancel">
          {{ t('cancel') }}
        </el-button>
        <el-button type="primary" size="large" @click="onSubmit">
          {{ t('confirm') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.special_td {
  padding: 10px;
  line-height: 20px;
  color: #000;
  background: #f7f7f7;
  border-radius: 3px;
}

.xxx {
  position: relative;
  float: left;
  padding: 3px;
  margin-bottom: 5px;
  cursor: pointer;
}

.flexBox {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: flex-start;
}

.roomList {
  height: 260px;
  padding: 5px;
  margin-top: 10px;
  overflow: auto;
  background-color: #f7f7f7;
}

.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    display: flex;
    // 让 page-main 的高度自适应
    flex: 1;
    flex-direction: column;
    overflow: auto;

    .search-container {
      margin-bottom: 0;
    }
  }
}

.page-main {
  .search-form {
    flex-wrap: wrap;
    margin-bottom: -18px;

    :deep(.el-form-item) {
      flex: 1 1 300px;

      &:last-child {
        margin-left: auto;

        .el-form-item__content {
          justify-content: flex-end;
        }
      }
    }
  }
}

.el-form--inline .el-form-item {
  margin-bottom: 5px;
}

/* 表单标签和子元素统一字体大小 */
.search-form {
  :deep(.el-form-item__label) {
    font-size: 14px !important;
  }

  :deep(.el-radio-button__inner) {
    font-size: 14px !important;
    height: 30px !important;
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px !important;
  }

  :deep(.el-checkbox) {
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
  }

  :deep(.el-checkbox__input) {
    display: flex !important;
    align-items: center !important;
  }
}

/* 房型按钮样式 */
.room-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 4px;

  .el-button {
    margin: 0;
    font-size: 14px !important;
    height: 30px !important;

    &.el-button--primary {
      &:hover {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
        box-shadow: none !important;
      }

      &:focus {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }

      &:active {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
      }

      &:focus-visible {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }
    }
  }
}

/* 其他文字元素统一字体大小 */
.special_td {
  font-size: 14px !important;
}

.roomtag {
  margin-right: 5px;
  margin-bottom: 8px;
  font-size: 14px !important;
  height: 30px !important;
  line-height: 30px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.roomList {
  font-size: 14px !important;

  .el-checkbox {
    font-size: 14px !important;
  }

  .el-checkbox__label {
    font-size: 14px !important;
  }
}
</style>
