<i18n>
{
	"en": {
		"printGuestBill": "Print Guest Bill",
		"cancel": "Cancel"
	},
	"zh-cn": {
		"printGuestBill": "打印宾客账单",
		"cancel": "取消"
	},
  "km": {
    "printGuestBill": "បោះពុម្ពវិក័យប័ត្រភ្ញៀវ",
    "cancel": "បោះបង់"
  }
}
</i18n>

<script setup lang="ts">
import { printApi, printFormApi } from '@/api/modules/index'
import { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { computed, createApp, nextTick, onMounted, ref } from 'vue'

import PosTogetherBill from './posTogetherBill.vue'

defineOptions({
  name: 'PrintCheckInForm',
})

const props = withDefaults(
  defineProps<{
    modelValue: boolean
    togetherCode: string | number
  }>(),
  {
    modelValue: false,
    togetherCode: '',
  }
)

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 声明 Stimulsoft 类型
// @ts-ignore
const Stimulsoft = window.Stimulsoft

const { t } = useI18n()
const userStore = useUserStore()
const data = ref({
  tableAutoHeight: true,
  showWidth: '600px',
})
const dialogWidth = computed(() => {
  return editDialogVisible.value ? '600px' : data.value.showWidth
})
const loading = ref(false)
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})

onMounted(async () => {
  await Promise.all([getPrintLayout(), getPrintInfo()])
})

const layout = ref(PrintFormat.POS.toString())
const isPosFormat = computed(() => layout.value === PrintFormat.POS.toString())

async function getPrintLayout() {
  printApi
    .getPrintLayout({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      billCode: BillType.BILL,
    })
    .then((res: any) => {
      if (res.code === 0) {
        layout.value = res.data.layout
        data.value.showWidth = layout_width_map.get(layout.value) ?? '400px'
      }
    })
}

const originalBillData = ref(null)
const editableBillData = ref(null)
const editDialogVisible = ref(false)

async function getPrintInfo() {
  loading.value = true
  printFormApi
    .printTogetherBill({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      togetherCode: props.togetherCode,
    })
    .then((res: any) => {
      if (res.code === 0) {
        originalBillData.value = JSON.parse(JSON.stringify(res.data))
        editableBillData.value = JSON.parse(JSON.stringify(res.data))
        setJson(res.data)
        loading.value = false
      }
    })
}

const timestamp = Date.now()
let reportInstance: any
const reportReady = ref(false)

async function setJson(json: any) {
  reportReady.value = false
  const licensePath = new URL('/src/assets/license.key', import.meta.url).href
  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)
  const report = new Stimulsoft.Report.StiReport()
  if (layout.value === PrintFormat.A4.toString()) {
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4.mrt?t=${timestamp}`)
  } else if (layout.value === PrintFormat.A412.toString()) {
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printTogetherBillA4-1-2.mrt?t=${timestamp}`)
  } else if (layout.value === PrintFormat.A413.toString()) {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBillA4-1-3.mrt?t=${timestamp}`)
  } else {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printTogetherBill78.mrt?t=${timestamp}`)
  }

  const settingsStore = useSettingsStore()
  const currentLanguage = settingsStore.lang
  const localizationFile = `${currentLanguage}.xml`
  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)

  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')
  dataSet.readJson(JSON.stringify(json))
  report.dictionary.databases.clear()
  report.regData('JSON', 'JSON', dataSet)
  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()
  viewerOptions.toolbar.visible = false
  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)
  viewer.report = report
  viewer.renderHtml('report')
  reportInstance = report
  loading.value = false
  reportReady.value = true
}

function openEditDialog() {
  editDialogVisible.value = true
}

async function onEditSave() {
  editDialogVisible.value = false
  await nextTick()
  setJson(editableBillData.value)
}

async function onEditCancel() {
  editDialogVisible.value = false
  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))
  await nextTick()
  setJson(editableBillData.value)
}

function printReport() {
  if (!reportReady.value || !reportInstance) {
    ElMessage.warning('报表还未加载完成，请稍后再试')
    return
  }
  reportInstance.print()
}

function onCancel() {
  myVisible.value = false
  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))
}

function removeConsumptionDetail(index: number) {
  editableBillData.value.consumptionDetails.splice(index, 1)
}

function addConsumptionDetail() {
  if (!editableBillData.value.consumptionDetails) {
    editableBillData.value.consumptionDetails = []
  }
  editableBillData.value.consumptionDetails.push({
    subCode: '',
    subName: '',
    fee: 0,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
  })
}

function removePaymentDetail(index: number) {
  editableBillData.value.paymentDetails.splice(index, 1)
}

function addPaymentDetail() {
  if (!editableBillData.value.paymentDetails) {
    editableBillData.value.paymentDetails = []
  }
  editableBillData.value.paymentDetails.push({
    subCode: '',
    subName: '',
    fee: 0,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
  })
}

function refreshReport() {
  setJson(editableBillData.value)
}
</script>

<template>
  <el-dialog v-model="myVisible" :width="dialogWidth" :close-on-click-modal="false" append-to-body destroy-on-close :show-close="true" style="min-height: 600px" @keydown.ctrl.enter.prevent="printReport">
    <div v-if="!editDialogVisible" style="display: flex; flex-direction: column; height: 100%">
      <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 10px">
        <el-button type="primary" :disabled="!reportReady" @click="printReport">
          {{ t('printGuestBill') }}
        </el-button>
        <el-button @click="onCancel">
          {{ t('cancel') }}
        </el-button>
        <el-button
          @click="
            () => {
              editDialogVisible = true
            }
          "
        >
          虚拟账单
        </el-button>
      </div>
      <div id="report" style="flex: 1; overflow-y: auto" />
    </div>
    <div v-else>
      <el-form :model="editableBillData" label-width="100px" style="height: 60vh; overflow-y: auto; padding: 20px">
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <el-form-item label="客人姓名">
            <el-input v-model="editableBillData.name" />
          </el-form-item>
          <el-form-item label="房号">
            <el-input v-model="editableBillData.rNo" />
          </el-form-item>
          <el-form-item label="房型">
            <el-input v-model="editableBillData.rtName" />
          </el-form-item>
          <el-form-item label="储值金额">
            <el-input v-model="editableBillData.balance" type="number" />
          </el-form-item>
          <el-form-item label="入住时间">
            <el-input v-model="editableBillData.checkinTime" />
          </el-form-item>
          <el-form-item label="离店时间">
            <el-input v-model="editableBillData.checkoutTime" />
          </el-form-item>
        </div>
        <div class="form-section">
          <h3 class="section-title">消费明细</h3>
          <el-table :data="editableBillData?.consumptionDetails || []" style="width: 100%; margin-bottom: 16px">
            <el-table-column prop="subName" label="账目" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.subName" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="时间" width="180">
              <template #default="scope">
                <el-input v-model="scope.row.createTime" />
              </template>
            </el-table-column>
            <el-table-column prop="fee" label="金额" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.fee" type="number" />
              </template>
            </el-table-column>
            <el-table-column width="80">
              <template #default="scope">
                <el-button type="danger" link @click="removeConsumptionDetail(scope.$index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-bottom: 16px">
            <el-button type="primary" @click="addConsumptionDetail"> 添加消费明细 </el-button>
          </div>
        </div>
        <div class="form-section">
          <h3 class="section-title">付款明细</h3>
          <el-table :data="editableBillData?.paymentDetails || []" style="width: 100%">
            <el-table-column prop="subName" label="账目" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.subName" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="时间" width="180">
              <template #default="scope">
                <el-input v-model="scope.row.createTime" />
              </template>
            </el-table-column>
            <el-table-column prop="fee" label="金额" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.fee" type="number" />
              </template>
            </el-table-column>
            <el-table-column width="80">
              <template #default="scope">
                <el-button type="danger" link @click="removePaymentDetail(scope.$index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 16px">
            <el-button type="primary" @click="addPaymentDetail"> 添加付款明细 </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <template v-if="!editDialogVisible">
        <!-- 报表页底部按钮（如无可省略） -->
      </template>
      <template v-else>
        <el-button @click="onEditCancel"> 复原 </el-button>
        <el-button type="primary" @click="onEditSave"> 保存账单 </el-button>
      </template>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
  }
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  padding: 0;
}

:deep(.el-dialog__footer) {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 1;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  .section-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}
</style>
