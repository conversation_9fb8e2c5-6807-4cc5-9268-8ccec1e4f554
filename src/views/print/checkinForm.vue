<i18n>
{
  "en": {
    "printCheckinRecord": "Print Check-in Record",
    "cancel": "Cancel",
    "shortcutKeyPrompt": "Support Ctrl+Enter shortcut key"
  },
  "zh-cn": {
    "printCheckinRecord": "打印入住单",
    "cancel": "取消",
    "shortcutKeyPrompt": "支持 Ctrl+Enter 快捷键"
  },
  "km": {
    "printCheckinRecord": "បោះពុម្ពកំណត់ត្រាចូលសំណាក",
    "cancel": "បោះបង់",
    "shortcutKeyPrompt": "គ្រប់គ្រងដោយ Ctrl+Enter"
  }
}
</i18n>

<script setup lang="ts">
import { printApi, printFormApi } from '@/api/modules/index'
import { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import PosCheckInForm78 from './posCheckInForm.vue' // 引入POS格式的自定义组件

defineOptions({
  name: 'PrintCheckInForm',
})

const props = withDefaults(
  defineProps<{
    modelValue: boolean
    orderNo: string | number
    subCode?: string
    fee?: number
    autoShowPrint?: boolean
    hideDialog?: boolean
  }>(),
  {
    modelValue: false,
    orderNo: '',
    subCode: '',
    fee: 0,
    autoShowPrint: false,
    hideDialog: false,
  }
)

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
  'print-started': []
}>()

const Stimulsoft = window.Stimulsoft

const { t } = useI18n() // 解构 t 函数
const userStore = useUserStore()
const data = ref({
  tableAutoHeight: true,
  showWidth: '400px',
})
const loading = ref(false)
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})

onMounted(async () => {
  await Promise.all([getPrintLayout(), getPrintInfo()])
})

const layout = ref(PrintFormat.POS.toString())
async function getPrintLayout() {
  printApi
    .getPrintLayout({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      billCode: BillType.CHECK_IN_FORM,
    })
    .then((res: any) => {
      if (res.code === 0) {
        layout.value = res.data.layout
        data.value.showWidth = layout_width_map.get(layout.value)
      }
    })
}

// 是否使用POS格式
const isPosFormat = computed(() => layout.value === PrintFormat.POS.toString())

// POS格式打印数据
const posFormData = ref(null)

async function getPrintInfo() {
  loading.value = true
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    orderNo: props.orderNo,
  }

  // 如果传入了subCode参数，则添加到请求参数中
  if (props.subCode) {
    params.subCode = props.subCode
  }

  // 如果传入了fee参数，则添加到请求参数中
  if (props.fee && props.fee > 0) {
    params.fee = props.fee
  }

  printFormApi.printCheckInForm(params).then((res: any) => {
    if (res.code === 0) {
      if (isPosFormat.value) {
        // 如果是POS格式，直接保存数据供自定义组件使用
        posFormData.value = res.data
        loading.value = false

        // 如果设置了自动打印，等待DOM更新后触发打印
        if (props.autoShowPrint) {
          nextTick(() => {
            setTimeout(() => {
              printReport()
            }, 800)
          })
        }
      } else {
        // 其他格式使用Stimulsoft报表
        setJson(res.data)
      }
    }
  })
}
const timestamp = Date.now()
let reportInstance: any
async function setJson(json: any) {
  // 只有在非POS格式时才执行报表渲染
  if (isPosFormat.value) {
    return
  }

  const licensePath = new URL('/src/assets/license.key', import.meta.url).href
  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)
  const report = new Stimulsoft.Report.StiReport()

  if (layout.value === PrintFormat.A4.toString()) {
    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4.mrt`)
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4.mrt?t=${timestamp}`)
  } else if (layout.value === PrintFormat.A412.toString()) {
    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4-1-2.mrt`)
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4-1-2.mrt?t=${timestamp}`)
  } else if (layout.value === PrintFormat.A413.toString()) {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinFormA4-1-3.mrt`)
    // report.loadFile(
    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinFormA4-1-3.mrt?t=${timestamp}`,
    // )
  } else {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printCheckinForm78.mrt`)
    // report.loadFile(
    //   `https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printCheckinForm78.mrt?t=${timestamp}`
    // )
  }

  const settingsStore = useSettingsStore() // 使用 Vuex 存储语言设置
  const currentLanguage = settingsStore.lang // 当前语言 'zh-cn' 或 'en'
  const localizationFile = `${currentLanguage}.xml`
  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)

  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')
  dataSet.readJson(JSON.stringify(json))
  report.dictionary.databases.clear()
  report.regData('JSON', 'JSON', dataSet)
  // 配置报表查看器选项
  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()
  viewerOptions.toolbar.visible = false // 隐藏工具栏
  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)
  viewer.report = report
  viewer.renderHtml('report')
  reportInstance = report // 保存报表实例
  loading.value = false

  // 如果设置了自动打印，等待报表渲染完成后触发打印
  if (props.autoShowPrint) {
    setTimeout(() => {
      printReport()
    }, 1000)
  }
}

function onCancel() {
  myVisible.value = false
}

function printReport() {
  // 通知父组件打印已开始
  emits('print-started')

  if (isPosFormat.value) {
    // 创建一个隐藏的iframe用于打印
    const printFrame = document.createElement('iframe')
    printFrame.style.position = 'fixed'
    printFrame.style.right = '0'
    printFrame.style.bottom = '0'
    printFrame.style.width = '0'
    printFrame.style.height = '0'
    printFrame.style.border = '0'

    document.body.appendChild(printFrame)
    const printContent = document.querySelector('#pos-checkin-form')?.innerHTML || ''
    // 检查contentWindow是否存在
    const contentWindow = printFrame.contentWindow
    if (!contentWindow) {
      console.error('打印窗口创建失败')
      return
    }

    const frameDoc = contentWindow.document
    frameDoc.write(`
        <html>
          <head>
          <meta charset="UTF-8">
            <title>入住登记单</title>
            <style>
              .pos-print-container {
                width: 76mm;
                margin: 0 auto;
                padding: 2mm;
                font-size: 14px;
                display: flex;
                flex-direction: column;
                align-items: center;
                color: black;
                padding: 5mm;
              }

              .hotel-title {
                text-align: center;
                font-size: 30px;
                font-weight: bold;
                margin-bottom: 5px;
              }

              .order-info {
                width: 95%;
                text-align: left;
                margin-bottom: 10px;
              }

              .order-no,
              .print-time {
                margin-top: 5px;
                text-align: left;
                font-size: 12px;
              }

              .form-title {
                text-align: center;
                font-size: 14px;
                margin-bottom: 10px;
              }

              .info-table {
                width: 95%;
                border-collapse: collapse;
                margin-bottom: 10px;
                margin-left: auto;
                margin-right: auto;
              }

              .info-table,
              .info-table th,
              .info-table td {
                border: 1px solid #000;
              }

              .info-table th {
                text-align: left;
                font-weight: bold;
                font-size: 12px;
                width: 25%;
              }

              .info-table td {
                padding: 3px 5px;
                text-align: left;
                font-size: 12px;
              }

              .section-title {
                text-align: left;
                font-weight: bold;
                margin: 10px 0 5px 0;
                width: 95%;
              }

              .together-guests {
                margin-top: 10px;
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
              }

              .together-table {
                width: 95%;
              }

              .footer {
                width: 95%;
                margin-top: 10px;
                font-size: 12px;
              }

              .footer-line {
                margin-bottom: 10px;
              }
              @media print {
                @page { size: 80mm auto; margin: 0; }
              }
            </style>
          </head>
          <body>
            ${printContent}
          </body>
        </html>
      `)
    frameDoc.close()

    // 等待内容加载完成后打印
    printFrame.onload = () => {
      try {
        // 使用浏览器原生打印预览
        printFrame.contentWindow.print()

        // 打印完成后移除iframe并关闭组件
        setTimeout(() => {
          document.body.removeChild(printFrame)
          // 如果是隐藏模式，打印完成后自动关闭组件
          if (props.hideDialog) {
            myVisible.value = false
          }
        }, 1000)
      } catch (e) {
        console.error('打印失败:', e)
      }
    }
  } else {
    // 其他格式使用Stimulsoft报表打印
    reportInstance?.print()
    // 如果是隐藏模式，打印完成后自动关闭组件
    if (props.hideDialog) {
      setTimeout(() => {
        myVisible.value = false
      }, 2000)
    }
  }
}
</script>

<template>
  <!-- 当hideDialog为true时，使用隐藏的div容器 -->
  <div v-if="hideDialog" style="position: fixed; top: -9999px; left: -9999px; visibility: hidden">
    <!-- 报表区域 -->
    <div v-if="!isPosFormat" id="report" style="width: 800px; height: 600px" />

    <!-- POS格式使用自定义Vue组件 -->
    <div v-else id="pos-checkin-form" style="width: 800px; height: 600px">
      <PosCheckInForm78 v-if="posFormData" id="pos-bill-form" :form-data="posFormData" />
    </div>
  </div>

  <!-- 正常的弹窗模式 -->
  <el-dialog v-else v-model="myVisible" :close-on-click-modal="false" :width="data.showWidth" append-to-body destroy-on-close :show-close="true" style="min-height: 600px" @keydown.ctrl.enter.prevent="printReport">
    <div style="display: flex; flex-direction: column; height: 100%">
      <!-- 按钮区域 - 当autoShowPrint为true时隐藏 -->
      <div v-if="!autoShowPrint" style="display: flex; gap: 10px; justify-content: center; margin-bottom: 10px">
        <el-button type="primary" @click="printReport">
          {{ t('printCheckinRecord') }}
        </el-button>
        <el-button @click="onCancel">
          {{ t('cancel') }}
        </el-button>
      </div>
      <!-- 报表区域 -->
      <div v-if="!isPosFormat" id="report" style="flex: 1; overflow-y: auto" />

      <!-- POS格式使用自定义Vue组件 -->
      <div v-else id="pos-checkin-form" style="flex: 1; overflow-y: auto">
        <PosCheckInForm78 v-if="posFormData" id="pos-bill-form" :form-data="posFormData" />
        <div v-else class="loading-placeholder">
          <el-empty v-if="!loading" description="暂无数据" />
          <el-skeleton v-else :rows="10" animated />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
  }
}

.shortcut-icon {
  margin-left: 5px;
  color: #909399;
  font-size: 16px;
  vertical-align: middle;

  &:hover {
    color: var(--el-color-primary);
  }
}
</style>
