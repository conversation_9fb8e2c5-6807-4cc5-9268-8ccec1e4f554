<i18n>
{
	"en": {
		"printRoomBill": "Print Billing Details",
		"cancel": "Cancel"
	},
	"zh-cn": {
		"printRoomBill": "打印结账明细单",
		"cancel": "取消"
	},
  "km": {
    "printRoomBill": "បោះពុម្ពព័ត៌មានលម្អិតអំពីវិក័យប័ត្រ",
    "cancel": "បោះបង់"
  }
}
</i18n>

<script setup lang="ts">
import { printApi, printFormApi } from '@/api/modules/index'
import { BillType, layout_width_map, PrintFormat } from '@/models/dict/constants'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import PosBillForm from './posBillForm.vue' // 导入POS小票组件

defineOptions({
  name: 'PrintCheckInForm',
})

const props = withDefaults(
  defineProps<{
    modelValue: boolean
    orderNo: string | number
    autoShowPrint?: boolean
    hideDialog?: boolean
  }>(),
  {
    modelValue: false,
    orderNo: '',
    autoShowPrint: false,
    hideDialog: false,
  },
)

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
  'print-started': []
}>()

const Stimulsoft = window.Stimulsoft

const { t } = useI18n() // 解构 t 函数
const userStore = useUserStore()
const data = ref({
  tableAutoHeight: true,
  showWidth: '800px',
})
const loading = ref(false)
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  },
})

const originalBillData = ref(null)
const editableBillData = ref(null)
const editDialogVisible = ref(false)
const posBillData = ref(null)

onMounted(async () => {
  await Promise.all([getPrintLayout(), getPrintInfo()])
})

const layout = ref(PrintFormat.POS.toString())
// 添加POS格式判断
const isPosFormat = computed(() => {
  return layout.value === PrintFormat.POS.toString()
})

async function getPrintLayout() {
  printApi
    .getPrintLayout({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      billCode: BillType.BILL,
    })
    .then((res: any) => {
      if (res.code === 0) {
        layout.value = res.data.layout
        data.value.showWidth = layout_width_map.get(layout.value)?.toString() || '400px'
      }
    })
}

async function getPrintInfo() {
  loading.value = true
  try {
    const res = await printFormApi.printSingleRoomBill({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      orderNo: props.orderNo,
    })
    if (res.code === 0) {
      originalBillData.value = JSON.parse(JSON.stringify(res.data))
      editableBillData.value = JSON.parse(JSON.stringify(res.data))
      if (isPosFormat.value) {
        posBillData.value = res.data

        // 如果设置了自动打印，等待DOM更新后触发打印
        if (props.autoShowPrint) {
          nextTick(() => {
            setTimeout(() => {
              printReport()
            }, 800)
          })
        }
      }
      else {
        setJson(res.data)
      }
    }
  }
  catch (error) {
    console.error('获取打印数据出错:', error)
    ElMessage.error('获取打印数据出错')
  }
  finally {
    loading.value = false
  }
}

const dialogWidth = computed(() => {
  return editDialogVisible.value ? '800px' : data.value.showWidth
})

const timestamp = Date.now()
let reportInstance: any
const reportReady = ref(false)

async function setJson(json: any) {
  reportReady.value = false
  const licensePath = new URL('/src/assets/license.key', import.meta.url).href
  await Stimulsoft.Base.StiLicense.loadFromFile(licensePath)
  const report = new Stimulsoft.Report.StiReport()

  if (layout.value === PrintFormat.A4.toString()) {
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printBillA4.mrt?t=${timestamp}`)
    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4.mrt`)
  }
  else if (layout.value === PrintFormat.A412.toString()) {
    report.loadFile(`https://aflower-**********.cos.ap-guangzhou.myqcloud.com/report-mrt/print/printBillA4-1-2.mrt?t=${timestamp}`)
    // report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4-1-2.mrt`)
  }
  else if (layout.value === PrintFormat.A413.toString()) {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBillA4-1-3.mrt`)
  }
  else {
    report.loadFile(`${import.meta.env.VITE_BASE_PREFIX}reports/print/printBill78.mrt`)
  }

  const settingsStore = useSettingsStore()
  const currentLanguage = settingsStore.lang
  const localizationFile = `${currentLanguage}.xml`
  Stimulsoft.Base.Localization.StiLocalization.setLocalizationFile(localizationFile)

  const dataSet = new Stimulsoft.System.Data.DataSet('JSON')
  dataSet.readJson(JSON.stringify(json))
  report.dictionary.databases.clear()
  report.regData('JSON', 'JSON', dataSet)

  const viewerOptions = new Stimulsoft.Viewer.StiViewerOptions()
  viewerOptions.toolbar.visible = false
  const viewer = new Stimulsoft.Viewer.StiViewer(viewerOptions, 'StiViewer', false)
  viewer.report = report
  viewer.renderHtml('report')
  reportInstance = report
  loading.value = false
  reportReady.value = true

  // 如果设置了自动打印，等待报表渲染完成后触发打印
  if (props.autoShowPrint) {
    setTimeout(() => {
      printReport()
    }, 1000)
  }
}

function printReport() {
  // 通知父组件打印已开始
  emits('print-started')

  if (isPosFormat.value) {
    // 创建一个隐藏的iframe用于打印
    const printFrame = document.createElement('iframe')
    printFrame.style.position = 'fixed'
    printFrame.style.right = '0'
    printFrame.style.bottom = '0'
    printFrame.style.width = '0'
    printFrame.style.height = '0'
    printFrame.style.border = '0'

    document.body.appendChild(printFrame)
    const printContent = document.querySelector('#pos-bill-form')?.innerHTML || ''
    const frameDoc = printFrame.contentWindow?.document
    if (!frameDoc) {
      console.error('无法访问打印框架的文档对象')
      return
    }
    frameDoc.write(`
        <html>
        <meta charset="UTF-8">
          <head>
            <title>结账明细单</title>
            <style>
              body {
                margin: 0;
                padding: 0;
              }
              .pos-bill-container {
                width: 70mm;
                margin: 0 auto;
                padding: 5mm;
                font-size: 14px;
                color: black;
              }
              .hotel-title {
                text-align: center;
                font-size: 30px;
                font-weight: bold;
                margin-bottom: 5px;
              }
              .form-title {
                text-align: center;
                font-size: 14px;
                margin-bottom: 4px;
              }
              .bill-info {
                margin-bottom: 4px;
                font-size: 12px;
              }
              .info-table,
              .detail-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 4px;
                font-size: 12px;
              }
              .info-table th,
              .info-table td,
              .detail-table th,
              .detail-table td {
                border: 1px solid #000;
                padding: 1px 2px;
              }
              .info-table th {
                width: 25%;
                text-align: left;
              }
              .center-text {
                text-align: center;
              }
              .total-row {
                font-weight: bold;
              }
              .remark {
                border: 1px solid #000;
                padding: 2px;
                margin-bottom: 4px;
                font-size: 10px;
              }
              .footer {
                width: 95%;
                margin-top: 10px;
                font-size: 12px;
              }
              .footer > div {
                margin-bottom: 10px;
              }
              @media print {
                @page {
                  size: 58mm auto;
                  margin: 0mm;
                }
                body {
                  margin: 0;
                  padding: 0;
                }
              }
            </style>
          </head>
          <body>
            ${printContent}
          </body>
        </html>
      `)
    frameDoc.close()

    printFrame.onload = () => {
      try {
        if (printFrame.contentWindow) {
          printFrame.contentWindow.print()
        }
        else {
          console.error('打印窗口未能正确创建')
        }
        setTimeout(() => {
          document.body.removeChild(printFrame)
          // 如果是隐藏模式，打印完成后自动关闭组件
          if (props.hideDialog) {
            myVisible.value = false
          }
        }, 1000)
      }
      catch (e) {
        console.error('打印失败:', e)
      }
    }
  }
  else {
    if (!reportReady.value || !reportInstance) {
      ElMessage.warning('报表还未加载完成，请稍后再试')
      return
    }
    reportInstance.print()
    // 如果是隐藏模式，打印完成后自动关闭组件
    if (props.hideDialog) {
      setTimeout(() => {
        myVisible.value = false
      }, 2000)
    }
  }
}

function onCancel() {
  myVisible.value = false
}

function openEditDialog() {
  editDialogVisible.value = true
}

async function onEditSave() {
  editDialogVisible.value = false
  await nextTick()
  if (isPosFormat.value) {
    posBillData.value = JSON.parse(JSON.stringify(editableBillData.value))
  }
  else {
    setJson(editableBillData.value)
  }
}

async function onEditCancel() {
  editDialogVisible.value = false
  editableBillData.value = JSON.parse(JSON.stringify(originalBillData.value))
  await nextTick()
  if (isPosFormat.value) {
    posBillData.value = JSON.parse(JSON.stringify(originalBillData.value))
  }
  else {
    setJson(originalBillData.value)
  }
}

function addConsumptionDetail() {
  if (!editableBillData.value.consumptionDetails) {
    editableBillData.value.consumptionDetails = []
  }
  editableBillData.value.consumptionDetails.push({
    subCode: '',
    subName: '',
    fee: 0,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
  })
  calculateTotals()
}

function removeConsumptionDetail(index: number) {
  editableBillData.value.consumptionDetails.splice(index, 1)
  calculateTotals()
}

function addPaymentDetail() {
  if (!editableBillData.value.paymentDetails) {
    editableBillData.value.paymentDetails = []
  }
  editableBillData.value.paymentDetails.push({
    subCode: '',
    subName: '',
    fee: 0,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
  })
  calculateTotals()
}

function removePaymentDetail(index: number) {
  editableBillData.value.paymentDetails.splice(index, 1)
  calculateTotals()
}

// 计算合计金额
function calculateTotals() {
  // 计算消费总额
  const consumeTotal
    = editableBillData.value.consumptionDetails?.reduce((sum, item) => {
      return sum + (Number(item.fee) || 0)
    }, 0) || 0
  editableBillData.value.consumeTotalFee = Number(consumeTotal.toFixed(2))

  // 计算支付总额
  const payTotal
    = editableBillData.value.paymentDetails?.reduce((sum, item) => {
      return sum + (Number(item.fee) || 0)
    }, 0) || 0
  editableBillData.value.payTotalFee = Number(payTotal.toFixed(2))
}

// 监听明细数据变化
watch(
  () => editableBillData.value?.consumptionDetails,
  () => {
    calculateTotals()
  },
  { deep: true },
)

watch(
  () => editableBillData.value?.paymentDetails,
  () => {
    calculateTotals()
  },
  { deep: true },
)
</script>

<template>
  <!-- 当hideDialog为true时，使用隐藏的div容器 -->
  <div v-if="hideDialog" style="position: fixed; top: -9999px; left: -9999px; visibility: hidden">
    <!-- 报表区域 -->
    <div v-if="!isPosFormat" id="report" style="width: 800px; height: 600px" />

    <!-- POS格式使用自定义Vue组件 -->
    <div v-else id="pos-bill-form" style="width: 800px; height: 600px">
      <PosBillForm v-if="posBillData" :form-data="posBillData" />
    </div>
  </div>

  <!-- 正常的弹窗模式 -->
  <el-dialog v-else v-model="myVisible" :width="dialogWidth" :close-on-click-modal="false" append-to-body destroy-on-close :show-close="true" style="min-height: 600px" @keydown.ctrl.enter.prevent="printReport">
    <div v-if="!editDialogVisible" style="display: flex; flex-direction: column; height: 100%">
      <!-- 按钮区域 - 当autoShowPrint为true时隐藏 -->
      <div v-if="!autoShowPrint" style="display: flex; gap: 10px; justify-content: center; margin-bottom: 10px">
        <el-button type="primary" :disabled="!reportReady && !isPosFormat" @click="printReport">
          {{ t('printRoomBill') }}
        </el-button>
        <el-button @click="onCancel">
          {{ t('cancel') }}
        </el-button>
        <el-button @click="openEditDialog">
          虚拟账单
        </el-button>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="isPosFormat" id="pos-bill-form" style="flex: 1; overflow-y: auto">
        <PosBillForm v-if="posBillData" :form-data="posBillData" />
        <el-empty v-else description="暂无数据" />
      </div>

      <div v-else id="report" style="flex: 1; overflow-y: auto" />
    </div>
    <div v-else style="padding: 20px; height: 60vh; overflow-y: auto">
      <el-form :model="editableBillData" label-width="120px">
        <div class="form-section">
          <h3 class="section-title">
            基本信息
          </h3>
          <div class="form-grid">
            <el-form-item label="酒店名称">
              <el-input v-model="editableBillData.hname" disabled />
            </el-form-item>
            <el-form-item label="客人姓名">
              <el-input v-model="editableBillData.name" />
            </el-form-item>
            <el-form-item label="房号">
              <el-input v-model="editableBillData.rNo" />
            </el-form-item>
            <el-form-item label="楼栋">
              <el-input v-model="editableBillData.buildingName" />
            </el-form-item>
            <el-form-item label="楼层">
              <el-input v-model="editableBillData.floorName" />
            </el-form-item>
            <el-form-item label="房型">
              <el-input v-model="editableBillData.rtName" />
            </el-form-item>
            <el-form-item label="客源类型">
              <el-input v-model="editableBillData.guestSrcTypeName" />
            </el-form-item>
            <el-form-item label="储值金额">
              <el-input v-model="editableBillData.balance" type="number" />
            </el-form-item>
            <el-form-item label="积分">
              <el-input v-model="editableBillData.point" type="number" />
            </el-form-item>
            <el-form-item label="消费总额">
              <el-input v-model="editableBillData.consumeTotalFee" type="number" />
            </el-form-item>
            <el-form-item label="入住时间">
              <el-date-picker v-model="editableBillData.checkinTime" type="datetime" placeholder="选择入住时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="离店时间">
              <el-date-picker v-model="editableBillData.checkoutTime" type="datetime" placeholder="选择离店时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
            <el-form-item label="支付总额">
              <el-input v-model="editableBillData.payTotalFee" type="number" />
            </el-form-item>
          </div>
          <el-form-item label="备注信息" class="full-width">
            <el-input v-model="editableBillData.info" type="textarea" :rows="3" />
          </el-form-item>
        </div>

        <div class="form-section">
          <h3 class="section-title">
            消费明细
          </h3>
          <el-table :data="editableBillData?.consumptionDetails || []" style="width: 100%; margin-bottom: 16px">
            <el-table-column prop="subName" label="账目" min-width="160">
              <template #default="scope">
                <el-input v-model="scope.row.subName" />
              </template>
            </el-table-column>
            <el-table-column prop="fee" label="金额" min-width="100">
              <template #default="scope">
                <el-input v-model="scope.row.fee" type="number" @input="calculateTotals" />
              </template>
            </el-table-column>
            <el-table-column width="70">
              <template #default="scope">
                <el-button type="danger" link size="small" @click="removeConsumptionDetail(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-bottom: 16px">
            <el-button type="primary" @click="addConsumptionDetail">
              添加消费明细
            </el-button>
          </div>
        </div>

        <div class="form-section">
          <h3 class="section-title">
            付款明细
          </h3>
          <el-table :data="editableBillData?.paymentDetails || []" style="width: 100%">
            <el-table-column prop="subName" label="账目" min-width="160">
              <template #default="scope">
                <el-input v-model="scope.row.subName" />
              </template>
            </el-table-column>
            <el-table-column prop="fee" label="金额" min-width="100">
              <template #default="scope">
                <el-input v-model="scope.row.fee" type="number" @input="calculateTotals" />
              </template>
            </el-table-column>
            <el-table-column width="70">
              <template #default="scope">
                <el-button type="danger" link size="small" @click="removePaymentDetail(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 16px">
            <el-button type="primary" @click="addPaymentDetail">
              添加付款明细
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <template v-if="!editDialogVisible">
        <!-- 报表页底部按钮（如无可省略） -->
      </template>
      <template v-else>
        <el-button @click="onEditCancel">
          复原
        </el-button>
        <el-button type="primary" @click="onEditSave">
          保存账单
        </el-button>
      </template>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.loading-container {
  padding: 20px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  padding: 0;
}

:deep(.el-dialog__footer) {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 1;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  .section-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行 2 个输入框 */
  gap: 16px; /* 行间距和列间距 */
  max-width: 100%; /* 适应容器宽度 */
}

.full-width {
  grid-column: 1 / -1;
}

.el-form-item {
  width: 100%;
}
</style>
