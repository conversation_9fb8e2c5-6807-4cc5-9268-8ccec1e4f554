import api from '../../../index.ts'

export default {

  // 获得班次设置列表
  getShiftTimeList: (data: any) => api.get('/admin-api/pms/shift-time/list', { params: data }),

  /**
   * 获得班次设置
   * @param data
   */
  getShiftTime: (data: {
    gcode: string
    hcode: string
    shiftCode: string
  }) => api.get('/admin-api/pms/shift-time/get', { params: data }),

  // 更新班次设置
  updateShiftTime: (data: any) => api.put('/admin-api/pms/shift-time/update', data),

  // 获得交班报表
  handoverReport: (data: any) => api.get('/admin-api/pms/account/handover-report', { params: data }),

	// 获得交班报表(收付实现制)
	handoverReportCashRealization: (data: any) => api.get('/admin-api/pms/account/handover-report-cash-realization', { params: data }),

	/**
	 * 获得可交互的班次
	 * @param data
	 */
	getChangeShiftTime: (data: {
		gcode: string
		hcode: string
	}) => api.get('/admin-api/pms/shift-time/get-change-shift', { params: data }),

	/**
	 * 交班
	 * @param data
	 */
	handover: (data: {
		gcode: string
		hcode: string
		shiftCode: string
	}) => api.post('/admin-api/pms/shift-time/handover', data),

	/**
	 * 交换班次
	 * @param data
	 * @param config - 可选的请求配置，包括请求头
	 */
	getChangeShiftByShiftSet: (data: {
		gcode: string
		hcode: string
	}, config?: any) => api.get('/admin-api/pms/shift-time/get-change-shift-by-shift-set', { params: data, ...config }),

	/**
	 * 获得已固化的班次
	 * @param data
	 * @param config - 可选的请求配置，包括请求头
	 */
	getHandoverReportList: (data: {
		gcode: string
		hcode: string
		bizDate?: string
	}, config?: any) => api.get('/report/handover-report/list', { params: data, ...config }),
}
